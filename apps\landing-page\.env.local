# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3003
NEXTAUTH_SECRET=development-secret-key-for-freela-syria

# Google OAuth Configuration (development placeholders)
GOOGLE_CLIENT_ID=development-client-id
GOOGLE_CLIENT_SECRET=development-client-secret

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/freela_syria

# API Configuration
API_BASE_URL=http://localhost:3003/api
