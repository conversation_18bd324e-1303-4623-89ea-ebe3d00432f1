2025-06-12T23:22:51.708124Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon")}
2025-06-12T23:22:51.708362Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:22:51.809402Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-06-12T23:22:51.809467Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:22:51.855103Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-12T23:22:52.026315Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-12T23:22:52.027143Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:22:54.809848Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:22:54.809991Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:00.912185Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:00.912214Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:01.008962Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:01.008985Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:01.209756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:01.209774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:01.309329Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:01.309355Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:01.509155Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:01.509179Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:01.609700Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:01.609721Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:01.708877Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:01.708898Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:01.810270Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:01.810383Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:01.909941Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:01.909961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:02.008157Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:02.008176Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:02.108540Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:02.108561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:02.209801Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:02.209821Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:02.309980Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:02.309997Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:02.408298Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:02.408317Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:02.509248Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:02.509262Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:02.609219Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:02.609234Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:02.709166Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:02.709286Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:02.808285Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:02.808301Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:03.008731Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:03.008749Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:03.109025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:03.109045Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:03.308555Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:03.308571Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:03.409235Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:03.409255Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:03.508331Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:03.508354Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:03.609265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:03.609284Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:03.708418Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:03.708433Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:03.809723Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:03.809740Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:03.908446Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:03.908461Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:04.109325Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:04.109345Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:04.208342Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:04.208360Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:04.309184Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:04.309304Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:04.409550Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:04.409569Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:04.508932Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:04.508970Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:04.708098Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:04.708117Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:04.909111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:04.909132Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:05.009860Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:05.009994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:05.209032Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:05.209248Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:05.308961Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:05.308979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:05.409379Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:05.409408Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:05.509227Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:05.509257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:05.608899Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:05.608920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:08.509006Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next\\_events.json")}
2025-06-12T23:23:08.509036Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-12T23:23:10.759912Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next")}
2025-06-12T23:23:10.760007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-12T23:23:10.760281Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-12T23:23:10.808219Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\i18n\\dist\\config.js"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.js.map")}
2025-06-12T23:23:10.808252Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/i18n"), path: AnchoredSystemPathBuf("packages\\i18n") }}))
2025-06-12T23:23:10.908961Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\i18n\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\index.js"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.js"), AnchoredSystemPathBuf("packages\\types\\dist\\index.js.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\user.js"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.d.ts.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\user.js.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.d.ts.map")}
2025-06-12T23:23:10.908994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/i18n"), path: AnchoredSystemPathBuf("packages\\i18n") }, WorkspacePackage { name: Other("@freela/types"), path: AnchoredSystemPathBuf("packages\\types") }}))
2025-06-12T23:23:10.909075Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-12T23:23:13.667328Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\types\\dist\\booking.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.js"), AnchoredSystemPathBuf("packages\\types\\dist\\client.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\user.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\client.js"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.js"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\client.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\api.js"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.js"), AnchoredSystemPathBuf("packages\\types\\dist\\api.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.js"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.js"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\api.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\api.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.js"), AnchoredSystemPathBuf("packages\\types\\dist\\service.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.js"), AnchoredSystemPathBuf("packages\\types\\dist\\service.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\user.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.js"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\client.js.map")}
2025-06-12T23:23:13.667366Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/types"), path: AnchoredSystemPathBuf("packages\\types") }}))
2025-06-12T23:23:13.667461Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-12T23:23:14.514108Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next\\_events.json")}
2025-06-12T23:23:14.514248Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-12T23:23:21.631742Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\database\\dist\\seed.js"), AnchoredSystemPathBuf("packages\\database\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.js.map"), AnchoredSystemPathBuf("packages\\database\\dist\\client.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\database\\dist\\client.d.ts"), AnchoredSystemPathBuf("packages\\database\\dist\\client.js"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.d.ts"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\client.d.ts.map"), AnchoredSystemPathBuf("apps\\landing-page\\.next"), AnchoredSystemPathBuf("packages\\database\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.d.ts"), AnchoredSystemPathBuf("packages\\database\\dist\\index.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\index.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.d.ts"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.d.ts.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.d.ts"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.js")}
2025-06-12T23:23:21.631786Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/utils"), path: AnchoredSystemPathBuf("packages\\utils") }, WorkspacePackage { name: Other("@freela/database"), path: AnchoredSystemPathBuf("packages\\database") }, WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-12T23:23:21.691247Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-12T23:23:22.409872Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-12")}
2025-06-12T23:23:22.409951Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:23:23.109430Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\trace"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\types"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\package.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\package.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\chunks\\polyfills.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\trace"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\types"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\chunks\\polyfills.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\server-reference-manifest.json")}
2025-06-12T23:23:23.109479Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/expert-dashboard"), path: AnchoredSystemPathBuf("apps\\expert-dashboard") }, WorkspacePackage { name: Other("@freela/admin-dashboard"), path: AnchoredSystemPathBuf("apps\\admin-dashboard") }}))
2025-06-12T23:24:24.182137Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-06-12T23:24:24.182221Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:24:24.278503Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-12T23:24:24.278544Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-12T23:24:24.431516Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
