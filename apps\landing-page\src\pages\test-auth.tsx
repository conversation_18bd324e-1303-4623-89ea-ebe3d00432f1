import React, { useState } from 'react';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useTranslation } from 'next-i18next';
import { ThemeProvider } from '@/themes';
import SignInModal from '@/components/auth/SignInModal';
import SignUpModal from '@/components/auth/SignUpModal';

const TestAuthPage: React.FC = () => {
  const [showSignIn, setShowSignIn] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);
  const { t } = useTranslation(['auth', 'common']);

  const handleAuthSuccess = (result: any) => {
    console.log('Auth success:', result);
    alert(`Authentication successful! Welcome ${result.user.email}`);
  };

  const handleAuthError = (error: string) => {
    console.error('Auth error:', error);
    alert(`Authentication error: ${error}`);
  };

  return (
    <ThemeProvider>
      <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full space-y-4">
          <h1 className="text-3xl font-bold text-white text-center mb-8">
            {t('common:testAuthentication')}
          </h1>
          
          <div className="space-y-4">
            <button
              onClick={() => setShowSignIn(true)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-colors"
            >
              Test Sign In Modal
            </button>
            
            <button
              onClick={() => setShowSignUp(true)}
              className="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg transition-colors"
            >
              Test Sign Up Modal
            </button>
          </div>

          <div className="mt-8 p-4 bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold text-white mb-2">Test Instructions:</h2>
            <ul className="text-gray-300 text-sm space-y-1">
              <li>• Click buttons to open authentication modals</li>
              <li>• Test Google OAuth integration</li>
              <li>• Verify glass morphism effects</li>
              <li>• Check Arabic RTL layout</li>
              <li>• Test dual-theme support (Gold/Purple)</li>
            </ul>
          </div>
        </div>

        {/* Sign In Modal */}
        <SignInModal
          isOpen={showSignIn}
          onClose={() => setShowSignIn(false)}
          onSuccess={handleAuthSuccess}
          onError={handleAuthError}
          onSwitchToSignUp={() => {
            setShowSignIn(false);
            setShowSignUp(true);
          }}
        />

        {/* Sign Up Modal */}
        <SignUpModal
          isOpen={showSignUp}
          onClose={() => setShowSignUp(false)}
          onSuccess={handleAuthSuccess}
          onError={handleAuthError}
          onSwitchToSignIn={() => {
            setShowSignUp(false);
            setShowSignIn(true);
          }}
        />
      </div>
    </ThemeProvider>
  );
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['auth', 'common'])),
    },
  };
};

export default TestAuthPage;
