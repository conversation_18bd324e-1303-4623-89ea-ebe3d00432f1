[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx": "11", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx": "12", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx": "13", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx": "14", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx": "15", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx": "16", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx": "17", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts": "18", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx": "19", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ThemeController\\index.tsx": "20", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\gold-theme.ts": "21", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\index.tsx": "22", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\purple-theme.ts": "23", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\theme-utils.ts": "24", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\types.ts": "25", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\AuthInput.tsx": "26", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\AuthModal.tsx": "27", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\index.ts": "28", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\SignInModal.tsx": "29", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\SignUpModal.tsx": "30", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\types\\auth.ts": "31", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\GoogleAuthButton.tsx": "32", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\providers\\SessionProvider.tsx": "33", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\api\\auth\\[...nextauth].ts": "34", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\test-auth.tsx": "35", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\types\\next-auth.d.ts": "36"}, {"size": 18375, "mtime": 1749658821771, "results": "37", "hashOfConfig": "38"}, {"size": 23363, "mtime": 1749679808123, "results": "39", "hashOfConfig": "38"}, {"size": 385, "mtime": 1749589496137, "results": "40", "hashOfConfig": "38"}, {"size": 21052, "mtime": 1749660702590, "results": "41", "hashOfConfig": "38"}, {"size": 24992, "mtime": 1749652496189, "results": "42", "hashOfConfig": "38"}, {"size": 14605, "mtime": 1749656490671, "results": "43", "hashOfConfig": "38"}, {"size": 16416, "mtime": 1749679817984, "results": "44", "hashOfConfig": "38"}, {"size": 16384, "mtime": 1749658840369, "results": "45", "hashOfConfig": "38"}, {"size": 15927, "mtime": 1749660219022, "results": "46", "hashOfConfig": "38"}, {"size": 22281, "mtime": 1749659106205, "results": "47", "hashOfConfig": "38"}, {"size": 16498, "mtime": 1749659136828, "results": "48", "hashOfConfig": "38"}, {"size": 3327, "mtime": 1749813340457, "results": "49", "hashOfConfig": "38"}, {"size": 3033, "mtime": 1749767264385, "results": "50", "hashOfConfig": "38"}, {"size": 1646, "mtime": 1749589446276, "results": "51", "hashOfConfig": "38"}, {"size": 2677, "mtime": 1749597314087, "results": "52", "hashOfConfig": "38"}, {"size": 2428, "mtime": 1749596227387, "results": "53", "hashOfConfig": "38"}, {"size": 989, "mtime": 1749596212722, "results": "54", "hashOfConfig": "38"}, {"size": 2120, "mtime": 1749597019188, "results": "55", "hashOfConfig": "38"}, {"size": 984, "mtime": 1749660691021, "results": "56", "hashOfConfig": "38"}, {"size": 9033, "mtime": 1749651379121, "results": "57", "hashOfConfig": "38"}, {"size": 5749, "mtime": 1749647330487, "results": "58", "hashOfConfig": "38"}, {"size": 4060, "mtime": 1749651461056, "results": "59", "hashOfConfig": "38"}, {"size": 5846, "mtime": 1749647373011, "results": "60", "hashOfConfig": "38"}, {"size": 5926, "mtime": 1749647406588, "results": "61", "hashOfConfig": "38"}, {"size": 3583, "mtime": 1749647289790, "results": "62", "hashOfConfig": "38"}, {"size": 7917, "mtime": 1749680529685, "results": "63", "hashOfConfig": "38"}, {"size": 8359, "mtime": 1749679762743, "results": "64", "hashOfConfig": "38"}, {"size": 313, "mtime": 1749766877904, "results": "65", "hashOfConfig": "38"}, {"size": 9512, "mtime": 1749767387781, "results": "66", "hashOfConfig": "38"}, {"size": 33891, "mtime": 1749770353073, "results": "67", "hashOfConfig": "38"}, {"size": 3592, "mtime": 1749766801764, "results": "68", "hashOfConfig": "38"}, {"size": 4763, "mtime": 1749813730215, "results": "69", "hashOfConfig": "38"}, {"size": 593, "mtime": 1749767211148, "results": "70", "hashOfConfig": "38"}, {"size": 4744, "mtime": 1749813766259, "results": "71", "hashOfConfig": "38"}, {"size": 3263, "mtime": 1749770548694, "results": "72", "hashOfConfig": "38"}, {"size": 690, "mtime": 1749813573532, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ody2rz", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx", [], ["182"], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts", ["183", "184", "185", "186", "187", "188", "189", "190"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ThemeController\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\gold-theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\purple-theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\theme-utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\AuthInput.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\AuthModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\SignInModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\SignUpModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\types\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\auth\\GoogleAuthButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\providers\\SessionProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\api\\auth\\[...nextauth].ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\test-auth.tsx", ["191", "192", "193"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\types\\next-auth.d.ts", [], [], {"ruleId": "194", "severity": 1, "message": "195", "line": 37, "column": 7, "nodeType": "196", "messageId": "197", "endLine": 37, "endColumn": 20, "suggestions": "198", "suppressions": "199"}, {"ruleId": "194", "severity": 1, "message": "195", "line": 7, "column": 30, "nodeType": "196", "messageId": "197", "endLine": 7, "endColumn": 43}, {"ruleId": "194", "severity": 1, "message": "195", "line": 8, "column": 29, "nodeType": "196", "messageId": "197", "endLine": 8, "endColumn": 41}, {"ruleId": "194", "severity": 1, "message": "195", "line": 37, "column": 5, "nodeType": "196", "messageId": "197", "endLine": 37, "endColumn": 18}, {"ruleId": "200", "severity": 1, "message": "201", "line": 37, "column": 31, "nodeType": "202", "messageId": "203", "endLine": 37, "endColumn": 34, "suggestions": "204"}, {"ruleId": "194", "severity": 1, "message": "195", "line": 44, "column": 5, "nodeType": "196", "messageId": "197", "endLine": 44, "endColumn": 17}, {"ruleId": "200", "severity": 1, "message": "201", "line": 44, "column": 30, "nodeType": "202", "messageId": "203", "endLine": 44, "endColumn": 33, "suggestions": "205"}, {"ruleId": "194", "severity": 1, "message": "195", "line": 73, "column": 3, "nodeType": "196", "messageId": "197", "endLine": 73, "endColumn": 16}, {"ruleId": "194", "severity": 1, "message": "195", "line": 74, "column": 3, "nodeType": "196", "messageId": "197", "endLine": 74, "endColumn": 15}, {"ruleId": "200", "severity": 1, "message": "201", "line": 14, "column": 38, "nodeType": "202", "messageId": "203", "endLine": 14, "endColumn": 41, "suggestions": "206"}, {"ruleId": "194", "severity": 1, "message": "195", "line": 15, "column": 5, "nodeType": "196", "messageId": "197", "endLine": 15, "endColumn": 16, "suggestions": "207"}, {"ruleId": "194", "severity": 1, "message": "195", "line": 20, "column": 5, "nodeType": "196", "messageId": "197", "endLine": 20, "endColumn": 18, "suggestions": "208"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["209"], ["210"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["211", "212"], ["213", "214"], ["215", "216"], ["217"], ["218"], {"messageId": "219", "data": "220", "fix": "221", "desc": "222"}, {"kind": "223", "justification": "224"}, {"messageId": "225", "fix": "226", "desc": "227"}, {"messageId": "228", "fix": "229", "desc": "230"}, {"messageId": "225", "fix": "231", "desc": "227"}, {"messageId": "228", "fix": "232", "desc": "230"}, {"messageId": "225", "fix": "233", "desc": "227"}, {"messageId": "228", "fix": "234", "desc": "230"}, {"messageId": "219", "data": "235", "fix": "236", "desc": "237"}, {"messageId": "219", "data": "238", "fix": "239", "desc": "222"}, "removeConsole", {"propertyName": "240"}, {"range": "241", "text": "224"}, "Remove the console.error().", "directive", "", "suggestUnknown", {"range": "242", "text": "243"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "244", "text": "245"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "246", "text": "243"}, {"range": "247", "text": "245"}, {"range": "248", "text": "243"}, {"range": "249", "text": "245"}, {"propertyName": "250"}, {"range": "251", "text": "224"}, "Remove the console.log().", {"propertyName": "240"}, {"range": "252", "text": "224"}, "error", [1086, 1152], [1100, 1103], "unknown", [1100, 1103], "never", [1286, 1289], [1286, 1289], [601, 604], [601, 604], "log", [615, 652], [780, 816]]