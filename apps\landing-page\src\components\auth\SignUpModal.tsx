import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { useTheme } from '@/themes';
import { userRegistrationSchema } from '@freela/utils';
import { z } from 'zod';
import AuthModal from './AuthModal';
import AuthInput from './AuthInput';
import GoogleAuthButton from './GoogleAuthButton';
import { RegisterFormData, AuthModalProps, PasswordStrength, SYRIAN_GOVERNORATES } from '@/types/auth';
import {
  UserIcon,
  EnvelopeIcon,
  LockClosedIcon,
  PhoneIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  UserGroupIcon,
  BriefcaseIcon
} from '@heroicons/react/24/outline';

interface SignUpModalProps extends AuthModalProps {
  onSwitchToSignIn: () => void;
}

const SignUpModal: React.FC<SignUpModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  onError,
  onSwitchToSignIn
}) => {
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: 'CLIENT',
    governorate: '',
    city: '',
    language: 'ar',
    acceptTerms: false
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  const { t } = useTranslation(['auth', 'common']);
  const { currentTheme, themeName } = useTheme();
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const isGoldTheme = themeName === 'gold';

  // Password strength calculation
  const passwordStrength = useMemo((): PasswordStrength => {
    const password = formData.password;
    let score = 0;
    
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[^A-Za-z0-9]/.test(password);
    const hasMinLength = password.length >= 8;

    if (hasUppercase) score++;
    if (hasLowercase) score++;
    if (hasNumber) score++;
    if (hasSpecialChar) score++;
    if (hasMinLength) score++;

    let label: 'weak' | 'fair' | 'good' | 'strong' = 'weak';
    if (score >= 4) label = 'strong';
    else if (score >= 3) label = 'good';
    else if (score >= 2) label = 'fair';

    return {
      score: Math.min(score, 4),
      label,
      hasUppercase,
      hasLowercase,
      hasNumber,
      hasSpecialChar,
      hasMinLength
    };
  }, [formData.password]);

  // Available cities based on selected governorate
  const availableCities = useMemo(() => {
    const selectedGov = SYRIAN_GOVERNORATES.find(gov => gov.governorate === formData.governorate);
    return selectedGov ? selectedGov.cities : [];
  }, [formData.governorate]);

  const handleInputChange = (field: keyof RegisterFormData, value: string | boolean) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // Reset city when governorate changes
      if (field === 'governorate') {
        newData.city = '';
      }
      
      return newData;
    });
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleInputBlur = (field: keyof RegisterFormData) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateField(field);
  };

  const validateField = (field: keyof RegisterFormData) => {
    try {
      if (field === 'confirmPassword') {
        if (formData.password !== formData.confirmPassword) {
          throw new Error(t('auth:validation.confirmPassword'));
        }
      } else {
        // Use the registration schema for validation
        const fieldSchema = userRegistrationSchema.shape[field as keyof typeof userRegistrationSchema.shape];
        if (fieldSchema) {
          fieldSchema.parse(formData[field]);
        }
      }
      setErrors(prev => ({ ...prev, [field]: '' }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors(prev => ({ 
          ...prev, 
          [field]: t(`auth:validation.${error.errors[0].code}`) || error.errors[0].message 
        }));
      } else if (error instanceof Error) {
        setErrors(prev => ({ ...prev, [field]: error.message }));
      }
    }
  };

  const validateStep = (step: number): boolean => {
    const fieldsToValidate: (keyof RegisterFormData)[] = [];
    
    if (step === 1) {
      fieldsToValidate.push('firstName', 'lastName', 'email', 'password', 'confirmPassword');
    } else if (step === 2) {
      fieldsToValidate.push('role');
      if (formData.phone) fieldsToValidate.push('phone');
    }

    let isValid = true;
    fieldsToValidate.forEach(field => {
      try {
        if (field === 'confirmPassword') {
          if (formData.password !== formData.confirmPassword) {
            throw new Error(t('auth:validation.confirmPassword'));
          }
        } else {
          const fieldSchema = userRegistrationSchema.shape[field as keyof typeof userRegistrationSchema.shape];
          if (fieldSchema) {
            fieldSchema.parse(formData[field]);
          }
        }
        setErrors(prev => ({ ...prev, [field]: '' }));
      } catch (error) {
        isValid = false;
        if (error instanceof z.ZodError) {
          setErrors(prev => ({ 
            ...prev, 
            [field]: t(`auth:validation.${error.errors[0].code}`) || error.errors[0].message 
          }));
        } else if (error instanceof Error) {
          setErrors(prev => ({ ...prev, [field]: error.message }));
        }
        setTouched(prev => ({ ...prev, [field]: true }));
      }
    });

    return isValid;
  };

  const validateForm = (): boolean => {
    try {
      userRegistrationSchema.parse(formData);
      
      // Additional validation for confirm password
      if (formData.password !== formData.confirmPassword) {
        setErrors(prev => ({ ...prev, confirmPassword: t('auth:validation.confirmPassword') }));
        return false;
      }
      
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as string] = t(`auth:validation.${err.code}`) || err.message;
          }
        });
        setErrors(newErrors);
        
        // Mark all fields as touched
        const touchedFields: Record<string, boolean> = {};
        Object.keys(formData).forEach(key => {
          touchedFields[key] = true;
        });
        setTouched(touchedFields);
      }
      return false;
    }
  };

  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock success
      if (onSuccess) {
        onSuccess({ user: { email: formData.email, role: formData.role }, token: 'mock-token' });
      }
      
      onClose();
    } catch (error) {
      const errorMessage = t('auth:register.networkError');
      setErrors({ general: errorMessage });
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const buttonClasses = `
    w-full h-14 rounded-xl font-semibold text-lg transition-all duration-300
    flex items-center justify-center gap-3 group relative overflow-hidden
    ${isRTL ? 'font-tajawal' : 'font-cairo'}
    ${isLoading ? 'cursor-not-allowed opacity-70' : 'cursor-pointer'}
  `;

  const primaryButtonStyle = {
    background: isGoldTheme
      ? 'linear-gradient(135deg, rgba(251, 191, 36, 0.9) 0%, rgba(245, 158, 11, 0.9) 100%)'
      : 'linear-gradient(135deg, rgba(147, 51, 234, 0.9) 0%, rgba(126, 34, 206, 0.9) 100%)',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    boxShadow: isGoldTheme
      ? '0 8px 32px rgba(251, 191, 36, 0.3)'
      : '0 8px 32px rgba(147, 51, 234, 0.3)',
  };

  const secondaryButtonStyle = {
    background: 'rgba(255, 255, 255, 0.08)',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
  };

  return (
    <AuthModal
      isOpen={isOpen}
      onClose={onClose}
      title={t('auth:register.title')}
      subtitle={t('auth:register.subtitle')}
      description={t('auth:register.description')}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Progress Indicator */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center gap-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold
                    transition-all duration-300
                    ${step <= currentStep 
                      ? 'text-white' 
                      : 'text-white/40'
                    }
                  `}
                  style={{
                    background: step <= currentStep 
                      ? (isGoldTheme 
                          ? 'linear-gradient(135deg, rgba(251, 191, 36, 0.9), rgba(245, 158, 11, 0.9))'
                          : 'linear-gradient(135deg, rgba(147, 51, 234, 0.9), rgba(126, 34, 206, 0.9))')
                      : 'rgba(255, 255, 255, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  {step}
                </div>
                {step < 3 && (
                  <div 
                    className={`w-8 h-0.5 mx-2 transition-all duration-300 ${
                      step < currentStep ? 'opacity-100' : 'opacity-30'
                    }`}
                    style={{
                      background: step < currentStep 
                        ? (isGoldTheme ? 'rgba(251, 191, 36, 0.6)' : 'rgba(147, 51, 234, 0.6)')
                        : 'rgba(255, 255, 255, 0.2)'
                    }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* General Error */}
        {errors.general && (
          <motion.div
            className="p-4 rounded-xl bg-red-500/10 border border-red-500/20 text-red-400 text-center"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            {errors.general}
          </motion.div>
        )}

        {/* Step Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: isRTL ? 20 : -20 }}
          transition={{ duration: 0.3 }}
        >
          {currentStep === 1 && (
            <div className="space-y-6">
              {/* Name Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <AuthInput
                  label={t('auth:register.firstName')}
                  placeholder={t('auth:register.firstNamePlaceholder')}
                  value={formData.firstName}
                  onChange={(value) => handleInputChange('firstName', value)}
                  onBlur={() => handleInputBlur('firstName')}
                  error={errors.firstName}
                  touched={touched.firstName}
                  required
                  leftIcon={<UserIcon className="w-5 h-5" />}
                />
                <AuthInput
                  label={t('auth:register.lastName')}
                  placeholder={t('auth:register.lastNamePlaceholder')}
                  value={formData.lastName}
                  onChange={(value) => handleInputChange('lastName', value)}
                  onBlur={() => handleInputBlur('lastName')}
                  error={errors.lastName}
                  touched={touched.lastName}
                  required
                  leftIcon={<UserIcon className="w-5 h-5" />}
                />
              </div>

              {/* Email */}
              <AuthInput
                label={t('auth:register.email')}
                placeholder={t('auth:register.emailPlaceholder')}
                type="email"
                value={formData.email}
                onChange={(value) => handleInputChange('email', value)}
                onBlur={() => handleInputBlur('email')}
                error={errors.email}
                touched={touched.email}
                required
                autoComplete="email"
                leftIcon={<EnvelopeIcon className="w-5 h-5" />}
              />

              {/* Password */}
              <AuthInput
                label={t('auth:register.password')}
                placeholder={t('auth:register.passwordPlaceholder')}
                type="password"
                value={formData.password}
                onChange={(value) => handleInputChange('password', value)}
                onBlur={() => handleInputBlur('password')}
                error={errors.password}
                touched={touched.password}
                required
                autoComplete="new-password"
                leftIcon={<LockClosedIcon className="w-5 h-5" />}
                showPasswordStrength={true}
                passwordStrength={{
                  score: passwordStrength.score,
                  label: t(`auth:passwordStrength.${passwordStrength.label}`)
                }}
              />

              {/* Confirm Password */}
              <AuthInput
                label={t('auth:register.confirmPassword')}
                placeholder={t('auth:register.confirmPasswordPlaceholder')}
                type="password"
                value={formData.confirmPassword}
                onChange={(value) => handleInputChange('confirmPassword', value)}
                onBlur={() => handleInputBlur('confirmPassword')}
                error={errors.confirmPassword}
                touched={touched.confirmPassword}
                required
                autoComplete="new-password"
                leftIcon={<LockClosedIcon className="w-5 h-5" />}
              />
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              {/* Phone Number */}
              <AuthInput
                label={t('auth:register.phone')}
                placeholder={t('auth:register.phonePlaceholder')}
                type="tel"
                value={formData.phone || ''}
                onChange={(value) => handleInputChange('phone', value)}
                onBlur={() => handleInputBlur('phone')}
                error={errors.phone}
                touched={touched.phone}
                leftIcon={<PhoneIcon className="w-5 h-5" />}
              />

              {/* Role Selection */}
              <div className="space-y-4">
                <label className={`block text-lg font-semibold ${isRTL ? 'text-right font-tajawal' : 'text-left font-cairo'}`}
                       style={{ color: currentTheme.colors.text.primary }}>
                  {t('auth:register.role')} <span className="text-red-400">*</span>
                </label>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Client Option */}
                  <motion.label
                    className={`
                      relative p-6 rounded-xl cursor-pointer transition-all duration-300
                      border-2 ${formData.role === 'CLIENT'
                        ? (isGoldTheme ? 'border-amber-400/50' : 'border-purple-400/50')
                        : 'border-white/20'
                      }
                    `}
                    style={{
                      background: formData.role === 'CLIENT'
                        ? (isGoldTheme
                            ? 'rgba(251, 191, 36, 0.1)'
                            : 'rgba(147, 51, 234, 0.1)')
                        : 'rgba(255, 255, 255, 0.05)',
                      backdropFilter: 'blur(20px)',
                      WebkitBackdropFilter: 'blur(20px)',
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <input
                      type="radio"
                      name="role"
                      value="CLIENT"
                      checked={formData.role === 'CLIENT'}
                      onChange={(e) => handleInputChange('role', e.target.value as 'CLIENT' | 'EXPERT')}
                      className="sr-only"
                    />
                    <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}>
                      <UserGroupIcon className="w-8 h-8 text-blue-400" />
                      <div>
                        <div className={`font-semibold text-lg ${isRTL ? 'font-tajawal' : 'font-cairo'}`}
                             style={{ color: currentTheme.colors.text.primary }}>
                          {t('auth:register.client')}
                        </div>
                      </div>
                    </div>
                  </motion.label>

                  {/* Expert Option */}
                  <motion.label
                    className={`
                      relative p-6 rounded-xl cursor-pointer transition-all duration-300
                      border-2 ${formData.role === 'EXPERT'
                        ? (isGoldTheme ? 'border-amber-400/50' : 'border-purple-400/50')
                        : 'border-white/20'
                      }
                    `}
                    style={{
                      background: formData.role === 'EXPERT'
                        ? (isGoldTheme
                            ? 'rgba(251, 191, 36, 0.1)'
                            : 'rgba(147, 51, 234, 0.1)')
                        : 'rgba(255, 255, 255, 0.05)',
                      backdropFilter: 'blur(20px)',
                      WebkitBackdropFilter: 'blur(20px)',
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <input
                      type="radio"
                      name="role"
                      value="EXPERT"
                      checked={formData.role === 'EXPERT'}
                      onChange={(e) => handleInputChange('role', e.target.value as 'CLIENT' | 'EXPERT')}
                      className="sr-only"
                    />
                    <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}>
                      <BriefcaseIcon className="w-8 h-8 text-green-400" />
                      <div>
                        <div className={`font-semibold text-lg ${isRTL ? 'font-tajawal' : 'font-cairo'}`}
                             style={{ color: currentTheme.colors.text.primary }}>
                          {t('auth:register.expert')}
                        </div>
                      </div>
                    </div>
                  </motion.label>
                </div>
                {errors.role && touched.role && (
                  <div className={`text-sm text-red-400 ${isRTL ? 'text-right font-tajawal' : 'text-left font-cairo'}`}>
                    {errors.role}
                  </div>
                )}
              </div>

              {/* Location */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Governorate */}
                <div className="space-y-2">
                  <label className={`block text-sm font-medium ${isRTL ? 'text-right font-tajawal' : 'text-left font-cairo'}`}
                         style={{ color: currentTheme.colors.text.secondary }}>
                    {t('auth:register.governorate')}
                  </label>
                  <select
                    value={formData.governorate}
                    onChange={(e) => handleInputChange('governorate', e.target.value)}
                    onBlur={() => handleInputBlur('governorate')}
                    className={`
                      w-full h-14 px-4 rounded-xl transition-all duration-300
                      ${isRTL ? 'text-right font-tajawal' : 'text-left font-cairo'}
                      text-white bg-white/8 border border-white/20
                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
                      ${isGoldTheme
                        ? 'focus:ring-amber-400/50 focus:border-amber-400/50'
                        : 'focus:ring-purple-400/50 focus:border-purple-400/50'
                      }
                    `}
                    style={{
                      backdropFilter: 'blur(20px)',
                      WebkitBackdropFilter: 'blur(20px)',
                    }}
                  >
                    <option value="">{t('auth:register.governoratePlaceholder')}</option>
                    {SYRIAN_GOVERNORATES.map((gov) => (
                      <option key={gov.governorate} value={gov.governorate} className="bg-gray-800">
                        {gov.governorate}
                      </option>
                    ))}
                  </select>
                </div>

                {/* City */}
                <div className="space-y-2">
                  <label className={`block text-sm font-medium ${isRTL ? 'text-right font-tajawal' : 'text-left font-cairo'}`}
                         style={{ color: currentTheme.colors.text.secondary }}>
                    {t('auth:register.city')}
                  </label>
                  <select
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    onBlur={() => handleInputBlur('city')}
                    disabled={!formData.governorate}
                    className={`
                      w-full h-14 px-4 rounded-xl transition-all duration-300
                      ${isRTL ? 'text-right font-tajawal' : 'text-left font-cairo'}
                      text-white bg-white/8 border border-white/20
                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
                      disabled:opacity-50 disabled:cursor-not-allowed
                      ${isGoldTheme
                        ? 'focus:ring-amber-400/50 focus:border-amber-400/50'
                        : 'focus:ring-purple-400/50 focus:border-purple-400/50'
                      }
                    `}
                    style={{
                      backdropFilter: 'blur(20px)',
                      WebkitBackdropFilter: 'blur(20px)',
                    }}
                  >
                    <option value="">{t('auth:register.cityPlaceholder')}</option>
                    {availableCities.map((city) => (
                      <option key={city} value={city} className="bg-gray-800">
                        {city}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              {/* Language Preference */}
              <div className="space-y-4">
                <label className={`block text-lg font-semibold ${isRTL ? 'text-right font-tajawal' : 'text-left font-cairo'}`}
                       style={{ color: currentTheme.colors.text.primary }}>
                  {t('auth:register.language')}
                </label>

                <div className="grid grid-cols-2 gap-4">
                  {/* Arabic */}
                  <motion.label
                    className={`
                      relative p-4 rounded-xl cursor-pointer transition-all duration-300
                      border-2 ${formData.language === 'ar'
                        ? (isGoldTheme ? 'border-amber-400/50' : 'border-purple-400/50')
                        : 'border-white/20'
                      }
                    `}
                    style={{
                      background: formData.language === 'ar'
                        ? (isGoldTheme
                            ? 'rgba(251, 191, 36, 0.1)'
                            : 'rgba(147, 51, 234, 0.1)')
                        : 'rgba(255, 255, 255, 0.05)',
                      backdropFilter: 'blur(20px)',
                      WebkitBackdropFilter: 'blur(20px)',
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <input
                      type="radio"
                      name="language"
                      value="ar"
                      checked={formData.language === 'ar'}
                      onChange={(e) => handleInputChange('language', e.target.value as 'ar' | 'en')}
                      className="sr-only"
                    />
                    <div className="text-center">
                      <div className={`font-semibold ${isRTL ? 'font-tajawal' : 'font-cairo'}`}
                           style={{ color: currentTheme.colors.text.primary }}>
                        {t('auth:register.arabic')}
                      </div>
                    </div>
                  </motion.label>

                  {/* English */}
                  <motion.label
                    className={`
                      relative p-4 rounded-xl cursor-pointer transition-all duration-300
                      border-2 ${formData.language === 'en'
                        ? (isGoldTheme ? 'border-amber-400/50' : 'border-purple-400/50')
                        : 'border-white/20'
                      }
                    `}
                    style={{
                      background: formData.language === 'en'
                        ? (isGoldTheme
                            ? 'rgba(251, 191, 36, 0.1)'
                            : 'rgba(147, 51, 234, 0.1)')
                        : 'rgba(255, 255, 255, 0.05)',
                      backdropFilter: 'blur(20px)',
                      WebkitBackdropFilter: 'blur(20px)',
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <input
                      type="radio"
                      name="language"
                      value="en"
                      checked={formData.language === 'en'}
                      onChange={(e) => handleInputChange('language', e.target.value as 'ar' | 'en')}
                      className="sr-only"
                    />
                    <div className="text-center">
                      <div className={`font-semibold ${isRTL ? 'font-tajawal' : 'font-cairo'}`}
                           style={{ color: currentTheme.colors.text.primary }}>
                        {t('auth:register.english')}
                      </div>
                    </div>
                  </motion.label>
                </div>
              </div>

              {/* Terms and Conditions */}
              <div className="space-y-4">
                <label className="flex items-start gap-3 cursor-pointer group">
                  <input
                    type="checkbox"
                    checked={formData.acceptTerms}
                    onChange={(e) => handleInputChange('acceptTerms', e.target.checked)}
                    className={`
                      mt-1 w-5 h-5 rounded border-white/20 bg-white/10
                      focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
                      ${isGoldTheme
                        ? 'text-amber-400 focus:ring-amber-400/50'
                        : 'text-purple-400 focus:ring-purple-400/50'
                      }
                    `}
                  />
                  <span className={`text-sm leading-relaxed ${isRTL ? 'font-tajawal text-right' : 'font-cairo text-left'}`}
                        style={{ color: currentTheme.colors.text.secondary }}>
                    {t('auth:register.acceptTerms')}{' '}
                    <button
                      type="button"
                      className="font-semibold transition-colors duration-200 hover:underline"
                      style={{ color: currentTheme.colors.text.accent }}
                    >
                      {t('auth:register.termsOfService')}
                    </button>
                    {' '}{t('auth:register.and')}{' '}
                    <button
                      type="button"
                      className="font-semibold transition-colors duration-200 hover:underline"
                      style={{ color: currentTheme.colors.text.accent }}
                    >
                      {t('auth:register.privacyPolicy')}
                    </button>
                  </span>
                </label>
                {errors.acceptTerms && touched.acceptTerms && (
                  <div className={`text-sm text-red-400 ${isRTL ? 'text-right font-tajawal' : 'text-left font-cairo'}`}>
                    {errors.acceptTerms}
                  </div>
                )}
              </div>
            </div>
          )}
        </motion.div>

        {/* Navigation Buttons */}
        <div className={`flex gap-4 ${currentStep === 1 ? 'justify-end' : 'justify-between'}`}>
          {currentStep > 1 && (
            <motion.button
              type="button"
              onClick={handlePrevStep}
              className={buttonClasses}
              style={secondaryButtonStyle}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              {isRTL ? (
                <ArrowRightIcon className="w-5 h-5" />
              ) : (
                <ArrowLeftIcon className="w-5 h-5" />
              )}
              {t('common:actions.previous')}
            </motion.button>
          )}

          {currentStep < 3 ? (
            <motion.button
              type="button"
              onClick={handleNextStep}
              className={buttonClasses}
              style={primaryButtonStyle}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              {t('common:actions.next')}
              {isRTL ? (
                <ArrowLeftIcon className="w-5 h-5" />
              ) : (
                <ArrowRightIcon className="w-5 h-5" />
              )}
            </motion.button>
          ) : (
            <motion.button
              type="submit"
              disabled={isLoading}
              className={buttonClasses}
              style={primaryButtonStyle}
              whileHover={!isLoading ? { scale: 1.02, y: -2 } : {}}
              whileTap={!isLoading ? { scale: 0.98 } : {}}
            >
              {isLoading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  {t('auth:register.registering')}
                </>
              ) : (
                <>
                  {t('auth:register.registerButton')}
                  {isRTL ? (
                    <ArrowLeftIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  ) : (
                    <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  )}
                </>
              )}
            </motion.button>
          )}
        </div>

        {/* Google OAuth - Only show on first step */}
        {currentStep === 1 && (
          <>
            {/* Divider */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-white/20" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span
                  className={`px-4 ${isRTL ? 'font-tajawal' : 'font-cairo'}`}
                  style={{
                    backgroundColor: currentTheme.colors.neutral[500],
                    color: currentTheme.colors.text.secondary
                  }}
                >
                  {t('auth:socialLogin.continueWith')}
                </span>
              </div>
            </div>

            {/* Google OAuth Button */}
            <GoogleAuthButton
              mode="signup"
              onLoading={(loading) => setIsLoading(loading)}
              onError={(error) => setErrors(prev => ({ ...prev, general: error }))}
              disabled={isLoading}
            />
          </>
        )}

        {/* Switch to Sign In */}
        <div className={`text-center ${isRTL ? 'font-tajawal' : 'font-cairo'}`}>
          <span style={{ color: currentTheme.colors.text.secondary }}>
            {t('auth:register.haveAccount')}
          </span>
          {' '}
          <button
            type="button"
            onClick={onSwitchToSignIn}
            className="font-semibold transition-colors duration-200 hover:underline"
            style={{ color: currentTheme.colors.text.accent }}
          >
            {t('auth:register.signIn')}
          </button>
        </div>
      </form>
    </AuthModal>
  );
};

export default SignUpModal;
