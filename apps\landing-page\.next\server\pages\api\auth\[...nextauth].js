"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "@next-auth/prisma-adapter":
/*!********************************************!*\
  !*** external "@next-auth/prisma-adapter" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@next-auth/prisma-adapter");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"@next-auth/prisma-adapter\");\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _freela_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @freela/database */ \"(api)/../../packages/database/dist/index.js\");\n/* harmony import */ var _freela_database__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_freela_database__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_2__.PrismaAdapter)(_freela_database__WEBPACK_IMPORTED_MODULE_3__.prisma),\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                if (account?.provider === \"google\") {\n                    // Check if user already exists\n                    const existingUser = await _freela_database__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                        where: {\n                            email: user.email || \"\"\n                        }\n                    });\n                    if (!existingUser) {\n                        // Create new user with Google OAuth data\n                        await _freela_database__WEBPACK_IMPORTED_MODULE_3__.prisma.user.create({\n                            data: {\n                                email: user.email || \"\",\n                                firstName: profile?.given_name || user.name?.split(\" \")[0] || \"\",\n                                lastName: profile?.family_name || user.name?.split(\" \").slice(1).join(\" \") || \"\",\n                                avatar: user.image ? {\n                                    url: user.image\n                                } : _prisma_client__WEBPACK_IMPORTED_MODULE_4__.Prisma.JsonNull,\n                                emailVerified: true,\n                                status: \"ACTIVE\",\n                                role: \"CLIENT\",\n                                language: \"ar\",\n                                passwordHash: \"\"\n                            }\n                        });\n                    } else {\n                        // Update existing user with Google data if needed\n                        await _freela_database__WEBPACK_IMPORTED_MODULE_3__.prisma.user.update({\n                            where: {\n                                email: user.email || \"\"\n                            },\n                            data: {\n                                avatar: user.image ? {\n                                    url: user.image\n                                } : existingUser.avatar || _prisma_client__WEBPACK_IMPORTED_MODULE_4__.Prisma.JsonNull,\n                                emailVerified: existingUser.emailVerified || true,\n                                lastLoginAt: new Date()\n                            }\n                        });\n                    }\n                }\n                return true;\n            } catch (error) {\n                // console.error('Error during sign in:', error);\n                return false;\n            }\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                // Fetch complete user data from database\n                const dbUser = await _freela_database__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                    where: {\n                        email: user.email || \"\"\n                    },\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        role: true,\n                        status: true,\n                        avatar: true,\n                        language: true\n                    }\n                });\n                if (dbUser) {\n                    token.id = dbUser.id;\n                    token.role = dbUser.role;\n                    token.status = dbUser.status;\n                    token.language = dbUser.language;\n                    token.firstName = dbUser.firstName;\n                    token.lastName = dbUser.lastName;\n                    token.avatar = dbUser.avatar ? JSON.stringify(dbUser.avatar) : undefined;\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.language = token.language;\n                session.user.firstName = token.firstName;\n                session.user.lastName = token.lastName;\n                session.user.avatar = token.avatar;\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Redirect to dashboard after successful authentication\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return `${baseUrl}/dashboard`;\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account: _account }) {\n            // console.log(`User ${user.email} signed in with ${account?.provider}`);\n            // Update last login time\n            if (user.email) {\n                await _freela_database__WEBPACK_IMPORTED_MODULE_3__.prisma.user.update({\n                    where: {\n                        email: user.email\n                    },\n                    data: {\n                        lastLoginAt: new Date()\n                    }\n                });\n            }\n        },\n        async signOut ({ token: _token }) {\n        // console.log(`User ${token?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(api)/../../packages/database/dist/client.js":
/*!**********************************************!*\
  !*** ../../packages/database/dist/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PrismaClient = exports.Prisma = exports.getPrismaInstance = exports.withTransaction = exports.checkDatabaseHealth = exports.disconnectDatabase = exports.connectDatabase = exports.prisma = void 0;\nconst client_1 = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\nObject.defineProperty(exports, \"PrismaClient\", ({ enumerable: true, get: function () { return client_1.PrismaClient; } }));\nObject.defineProperty(exports, \"Prisma\", ({ enumerable: true, get: function () { return client_1.Prisma; } }));\nlet prisma;\n// This is a singleton pattern to ensure we only have one instance of Prisma Client.\nconst getPrismaInstance = () => {\n    if (prisma) {\n        return prisma;\n    }\n    const newPrismaInstance = new client_1.PrismaClient({\n        log:  true ? ['query', 'error', 'warn'] : 0,\n        errorFormat: 'pretty',\n    });\n    // In development, use a global variable to preserve the client across hot reloads.\n    if (true) {\n        if (!globalThis.__prisma) {\n            globalThis.__prisma = newPrismaInstance;\n        }\n        prisma = globalThis.__prisma;\n    }\n    else {}\n    return prisma; // Non-null assertion since we just created it\n};\nexports.getPrismaInstance = getPrismaInstance;\n// Immediately get the instance to be used by the app\nconst prismaInstance = getPrismaInstance();\nexports.prisma = prismaInstance;\n// Graceful shutdown logic\nconst setupGracefulShutdown = (client) => {\n    let isShuttingDown = false;\n    const shutdown = async (signal) => {\n        if (isShuttingDown)\n            return;\n        isShuttingDown = true;\n        console.log(`Received ${signal}. Disconnecting database...`);\n        await client.$disconnect();\n        console.log('Database disconnected.');\n        process.exit(0);\n    };\n    process.on('SIGINT', () => shutdown('SIGINT'));\n    process.on('SIGTERM', () => shutdown('SIGTERM'));\n};\nsetupGracefulShutdown(prismaInstance);\n// Database connection utilities with timeout and graceful failure\nconst connectDatabase = async () => {\n    const client = getPrismaInstance();\n    try {\n        // Race connection against a timeout\n        await Promise.race([\n            client.$connect(),\n            new Promise((_, reject) => setTimeout(() => reject(new Error('Database connection timed out after 10 seconds')), 10000)),\n        ]);\n        console.log('✅ Database connected successfully');\n    }\n    catch (error) {\n        if (error instanceof Error) {\n            console.error('❌ Database connection failed:', error.message);\n        }\n        else {\n            console.error('❌ An unexpected error occurred during database connection:', error);\n        }\n        console.warn('⚠️ Server is starting without a database connection. Some features will be unavailable.');\n        // Do not re-throw; allow the application to start in a degraded state.\n        return; // Explicitly return to avoid any potential re-throw\n    }\n};\nexports.connectDatabase = connectDatabase;\nconst disconnectDatabase = async () => {\n    const client = getPrismaInstance();\n    try {\n        await client.$disconnect();\n        console.log('✅ Database disconnected successfully');\n    }\n    catch (error) {\n        if (error instanceof Error) {\n            console.error('❌ Database disconnection failed:', error.message);\n        }\n        else {\n            console.error('❌ An unexpected error occurred during database disconnection:', error);\n        }\n        // In a disconnect scenario, we should probably throw to indicate a problem.\n        throw error;\n    }\n};\nexports.disconnectDatabase = disconnectDatabase;\n// Health check for the database connection\nconst checkDatabaseHealth = async () => {\n    const client = getPrismaInstance();\n    try {\n        await client.$queryRaw `SELECT 1`;\n        return true;\n    }\n    catch (error) {\n        // Don't spam logs on health checks\n        return false;\n    }\n};\nexports.checkDatabaseHealth = checkDatabaseHealth;\n/**\n * Wrapper for Prisma transactions.\n * @template T\n * @param {(tx: any) => Promise<T>} callback\n * @returns {Promise<T>}\n */\nconst withTransaction = async (callback) => {\n    const client = getPrismaInstance();\n    return await client.$transaction(callback);\n};\nexports.withTransaction = withTransaction;\nexports[\"default\"] = prismaInstance;\n//# sourceMappingURL=client.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../packages/database/dist/client.js\n");

/***/ }),

/***/ "(api)/../../packages/database/dist/index.js":
/*!*********************************************!*\
  !*** ../../packages/database/dist/index.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n// Re-export all database utilities and the Prisma client instance.\n// This acts as the main entry point for the package.\n__exportStar(__webpack_require__(/*! ./client */ \"(api)/../../packages/database/dist/client.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi4vLi4vcGFja2FnZXMvZGF0YWJhc2UvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxvQ0FBb0M7QUFDbkQ7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RDtBQUNBO0FBQ0EsYUFBYSxtQkFBTyxDQUFDLDhEQUFVO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9sYW5kaW5nLXBhZ2UvLi4vLi4vcGFja2FnZXMvZGF0YWJhc2UvZGlzdC9pbmRleC5qcz83OWRjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9fY3JlYXRlQmluZGluZyA9ICh0aGlzICYmIHRoaXMuX19jcmVhdGVCaW5kaW5nKSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgdmFyIGRlc2MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG0sIGspO1xuICAgIGlmICghZGVzYyB8fCAoXCJnZXRcIiBpbiBkZXNjID8gIW0uX19lc01vZHVsZSA6IGRlc2Mud3JpdGFibGUgfHwgZGVzYy5jb25maWd1cmFibGUpKSB7XG4gICAgICBkZXNjID0geyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9O1xuICAgIH1cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIGRlc2MpO1xufSkgOiAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIG9bazJdID0gbVtrXTtcbn0pKTtcbnZhciBfX2V4cG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9fZXhwb3J0U3RhcikgfHwgZnVuY3Rpb24obSwgZXhwb3J0cykge1xuICAgIGZvciAodmFyIHAgaW4gbSkgaWYgKHAgIT09IFwiZGVmYXVsdFwiICYmICFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZXhwb3J0cywgcCkpIF9fY3JlYXRlQmluZGluZyhleHBvcnRzLCBtLCBwKTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyBSZS1leHBvcnQgYWxsIGRhdGFiYXNlIHV0aWxpdGllcyBhbmQgdGhlIFByaXNtYSBjbGllbnQgaW5zdGFuY2UuXG4vLyBUaGlzIGFjdHMgYXMgdGhlIG1haW4gZW50cnkgcG9pbnQgZm9yIHRoZSBwYWNrYWdlLlxuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2NsaWVudFwiKSwgZXhwb3J0cyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/../../packages/database/dist/index.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();