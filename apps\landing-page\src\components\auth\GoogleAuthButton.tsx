import React from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { useTheme } from '@/themes';
import { signIn } from 'next-auth/react';

interface GoogleAuthButtonProps {
  mode: 'signin' | 'signup';
  onLoading?: (loading: boolean) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
}

const GoogleAuthButton: React.FC<GoogleAuthButtonProps> = ({
  mode,
  onLoading,
  onError,
  disabled = false,
  className = ''
}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const { currentTheme, themeName } = useTheme();
  const router = useRouter();
  const { locale } = router;
  const { t } = useTranslation('auth');
  const isRTL = locale === 'ar';
  const isGoldTheme = themeName === 'gold';

  const handleGoogleAuth = async () => {
    if (disabled || isLoading) return;

    try {
      setIsLoading(true);
      onLoading?.(true);

      const result = await signIn('google', {
        redirect: false,
        callbackUrl: mode === 'signin' ? '/dashboard' : '/onboarding'
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      // Success will be handled by the callback
    } catch (error) {
      // console.error('Google authentication error:', error);
      const errorMessage = error instanceof Error 
        ? error.message 
        : t('login.networkError');
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
      onLoading?.(false);
    }
  };

  const buttonClasses = `
    w-full h-14 px-6 rounded-xl font-semibold text-base
    flex items-center justify-center gap-3 group
    transition-all duration-300 ease-out
    border border-white/20 backdrop-blur-xl
    ${isRTL ? 'font-tajawal flex-row-reverse' : 'font-sans'}
    ${disabled || isLoading 
      ? 'opacity-50 cursor-not-allowed' 
      : 'hover:scale-[1.02] hover:-translate-y-1 active:scale-[0.98] cursor-pointer'
    }
    ${className}
  `;

  const buttonStyle = {
    background: isGoldTheme
      ? 'linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%)'
      : 'linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(126, 34, 206, 0.05) 100%)',
    color: currentTheme.colors.text.primary,
    boxShadow: isGoldTheme
      ? '0 8px 32px rgba(251, 191, 36, 0.15)'
      : '0 8px 32px rgba(147, 51, 234, 0.15)',
  };

  const hoverStyle = {
    background: isGoldTheme
      ? 'linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(245, 158, 11, 0.08) 100%)'
      : 'linear-gradient(135deg, rgba(147, 51, 234, 0.15) 0%, rgba(126, 34, 206, 0.08) 100%)',
    boxShadow: isGoldTheme
      ? '0 12px 40px rgba(251, 191, 36, 0.25)'
      : '0 12px 40px rgba(147, 51, 234, 0.25)',
  };

  return (
    <motion.button
      type="button"
      onClick={handleGoogleAuth}
      disabled={disabled || isLoading}
      className={buttonClasses}
      style={buttonStyle}
      whileHover={!disabled && !isLoading ? hoverStyle : {}}
      whileTap={!disabled && !isLoading ? { scale: 0.98 } : {}}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {isLoading ? (
        <>
          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
          <span>{t('modal.loading')}</span>
        </>
      ) : (
        <>
          {/* Google Icon */}
          <svg
            className="w-5 h-5 flex-shrink-0"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              fill="#4285F4"
            />
            <path
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              fill="#34A853"
            />
            <path
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              fill="#FBBC05"
            />
            <path
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              fill="#EA4335"
            />
          </svg>
          
          <span>
            {mode === 'signin' 
              ? t('socialLogin.google')
              : t('socialLogin.google')
            }
          </span>
        </>
      )}
    </motion.button>
  );
};

export default GoogleAuthButton;
