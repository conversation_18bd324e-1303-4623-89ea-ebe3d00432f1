module.exports = {
  i18n: {
    defaultLocale: 'ar',
    locales: ['ar', 'en'],
    localeDetection: false,
  },
  fallbackLng: {
    default: ['ar'],
  },
  debug: process.env.NODE_ENV === 'development',
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  ns: ['common', 'landing', 'auth'],
  defaultNS: 'landing',
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false,
  },
};
